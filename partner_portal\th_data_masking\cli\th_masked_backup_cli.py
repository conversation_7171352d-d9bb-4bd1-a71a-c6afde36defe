#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import argparse
from datetime import datetime

# Thêm đường dẫn Odoo vào sys.path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

import odoo
from odoo import api, SUPERUSER_ID
from odoo.tools import config


class MaskedBackupCLI:
    """CLI cho việc tạo backup database với data masking"""
    
    def __init__(self, database_name, config_file=None):
        self.database_name = database_name
        self.config_file = config_file
        self.env = None
        
    def th_connect_database(self):
        """Kết nối đến database Odoo"""
        try:
            # Thiết lập config cho Odoo
            config_args = []
            if self.config_file:
                config_args = ['-c', self.config_file]
                print(f"Sử dụng config file: {self.config_file}")
            
            # Parse config với đường dẫn file config
            config.parse_config(config_args)
            
            # Khởi tạo Odoo registry
            registry = odoo.registry(self.database_name)
            
            with registry.cursor() as cr:
                self.env = api.Environment(cr, SUPERUSER_ID, {})
                
                # Kiểm tra module th_data_masking đã được cài đặt
                module = self.env['ir.module.module'].search([
                    ('name', '=', 'th_data_masking'),
                    ('state', '=', 'installed')
                ])
                
                if not module:
                    print(f"Module 'th_data_masking' chưa được cài đặt trong database '{self.database_name}'")
                    print("Vui lòng cài đặt module trước khi sử dụng CLI.")
                    return False
                
                # Kiểm tra model wizard có tồn tại
                if 'th.masked.backup.wizard' not in self.env:
                    print(f"Model 'th.masked.backup.wizard' không tồn tại")
                    print("Kiểm tra lại module th_data_masking")
                    return False
                
                print(f"Kết nối thành công database: {self.database_name}")
                print(f"Module th_data_masking đã sẵn sàng")
                return True
                
        except Exception as e:
            print(f"Lỗi kết nối database '{self.database_name}': {e}")
            print(f"Kiểm tra:")
            print(f"   - File config: {self.config_file}")
            print(f"   - Database tồn tại: {self.database_name}")
            print(f"   - PostgreSQL service đang chạy")
            return False
    
    def th_show_main_menu(self):
        """Hiển thị menu chính"""
        while True:
            print("\n" + "="*60)
            print("🔒 ODOO DATA MASKING BACKUP CLI")
            print("="*60)
            print("1. Tạo backup mới với data masking")
            print("2. Xem lịch sử backup")
            print("3. Xem chi tiết backup")
            print("4. Dọn dẹp backup cũ")
            print("5. Thoát")
            print("-"*60)
            
            choice = input("Chọn tùy chọn (1-5): ").strip()
            
            if choice == '1':
                self.th_create_backup_menu()
            elif choice == '2':
                self.th_show_backup_history()
            elif choice == '3':
                self.th_show_backup_details()
            elif choice == '4':
                self.th_cleanup_old_backups()
            elif choice == '5':
                print("Tạm biệt!")
                break
            else:
                print("Lựa chọn không hợp lệ!")
    
    def th_create_backup_menu(self):
        """Menu tạo backup mới"""
        print("\n" + "="*50)
        print("TẠO BACKUP MỚI")
        print("="*50)
        
        # Chọn định dạng backup
        print("\nChọn định dạng backup:")
        print("1. ZIP (bao gồm filestore)")
        print("2. SQL dump only")
        
        format_choice = input("Chọn định dạng (1-2): ").strip()
        backup_format = 'zip' if format_choice == '1' else 'dump'
        
        # Chọn masking rules
        print("\nChọn quy tắc masking:")
        print("1. Áp dụng tất cả quy tắc masking")
        print("2. Chọn quy tắc cụ thể")
        
        rule_choice = input("Chọn tùy chọn (1-2): ").strip()
        include_all_rules = rule_choice == '1'
        selected_rule_ids = []
        
        if not include_all_rules:
            selected_rule_ids = self.th_select_masking_rules()
            if not selected_rule_ids:
                print("Không có quy tắc nào được chọn. Quay lại menu chính.")
                return
        
        # Xác nhận tạo backup
        print(f"\nTHÔNG TIN BACKUP:")
        print(f"   Định dạng: {backup_format.upper()}")
        print(f"   Quy tắc: {'Tất cả quy tắc' if include_all_rules else f'{len(selected_rule_ids)} quy tắc được chọn'}")
        
        confirm = input("\nXác nhận tạo backup? (y/N): ").strip().lower()
        if confirm != 'y':
            print("Hủy tạo backup.")
            return
        
        # Tạo backup
        self.th_execute_backup(backup_format, include_all_rules, selected_rule_ids)
    
    def th_select_masking_rules(self):
        """Chọn masking rules cụ thể"""
        try:
            with odoo.registry(self.database_name).cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                
                # Lấy danh sách masking rules
                rules = env['th.data.masking.rule'].search([
                    ('active', '=', True),
                    ('th_apply_to_backups', '=', True)
                ])
                
                if not rules:
                    print("Không có quy tắc masking nào khả dụng.")
                    return []
                
                print(f"\nDANH SÁCH QUY TẮC MASKING ({len(rules)} quy tắc):")
                print("-"*60)
                for i, rule in enumerate(rules, 1):
                    field_count = len(rule.th_data_masking_rule_line_ids)
                    print(f"{i:2d}. {rule.name}")
                    print(f"     Model: {rule.model_id.name}")
                    print(f"     Số trường: {field_count}")
                    print()
                
                # Cho phép chọn nhiều rules
                print("Nhập số thứ tự các quy tắc (cách nhau bằng dấu phẩy, ví dụ: 1,3,5):")
                selection = input("Chọn quy tắc: ").strip()
                
                if not selection:
                    return []
                
                try:
                    indices = [int(x.strip()) for x in selection.split(',')]
                    selected_rules = []
                    
                    for idx in indices:
                        if 1 <= idx <= len(rules):
                            selected_rules.append(rules[idx-1].id)
                        else:
                            print(f"Bỏ qua số thứ tự không hợp lệ: {idx}")
                    
                    return selected_rules
                    
                except ValueError:
                    print("Định dạng không hợp lệ.")
                    return []
                    
        except Exception as e:
            print(f"Lỗi lấy danh sách masking rules: {e}")
            return []
    
    def th_execute_backup(self, backup_format, include_all_rules, selected_rule_ids):
        """Thực thi backup"""
        try:
            with odoo.registry(self.database_name).cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                
                # Tạo wizard
                wizard_vals = {
                    'th_backup_format': backup_format,
                    'th_include_all_masking_rules': include_all_rules,
                    'th_run_in_background': False,  # Chạy đồng bộ để theo dõi
                }
                
                if not include_all_rules and selected_rule_ids:
                    wizard_vals['th_masking_rule_ids'] = [(6, 0, selected_rule_ids)]
                
                wizard = env['th.masked.backup.wizard'].create(wizard_vals)
                
                print(f"\nBắt đầu tạo backup...")
                print(f"   Database: {self.database_name}")
                print(f"   Temp DB: {wizard.th_temp_db_name}")
                
                # Thực thi backup
                start_time = time.time()
                result = wizard.action_create_masked_backup()
                
                # Lấy backup record ID từ result
                if isinstance(result, dict) and 'res_id' in result:
                    backup_id = result['res_id']
                    self.th_monitor_backup_progress(backup_id)
                else:
                    print("Không thể lấy thông tin backup record.")
                    
        except Exception as e:
            print(f"Lỗi tạo backup: {e}")
    
    def th_monitor_backup_progress(self, backup_id):
        """Theo dõi tiến trình backup"""
        print("\nTHEO DÕI TIẾN TRÌNH:")
        print("-"*50)
        
        last_log_length = 0
        
        while True:
            try:
                with odoo.registry(self.database_name).cursor() as cr:
                    env = api.Environment(cr, SUPERUSER_ID, {})
                    backup = env['th.database.masked.backup'].browse(backup_id)
                    
                    if not backup.exists():
                        print("Không tìm thấy backup record.")
                        break
                    
                    # Hiển thị log mới
                    if backup.th_log:
                        log_lines = backup.th_log.replace('<br/>', '\n').strip().split('\n')
                        new_lines = log_lines[last_log_length:]
                        
                        for line in new_lines:
                            if line.strip():
                                timestamp = datetime.now().strftime('%H:%M:%S')
                                print(f"[{timestamp}] {line.strip()}")
                        
                        last_log_length = len(log_lines)
                    
                    # Kiểm tra trạng thái
                    if backup.th_status == 'done':
                        print(f"\nBACKUP HOÀN THÀNH!")
                        print(f"   File: {backup.th_filename}")
                        print(f"   Kích thước: {backup.th_file_size} KB")
                        print(f"   Thời gian: {backup.th_duration:.2f} phút")
                        print(f"   Số model đã mask: {backup.th_masked_field_count}")
                        break
                    elif backup.th_status == 'failed':
                        print(f"\nBACKUP THẤT BẠI!")
                        break
                    
                    time.sleep(2)  # Chờ 2 giây trước khi check lại
                    
            except Exception as e:
                print(f"Lỗi theo dõi tiến trình: {e}")
                break
    
    def th_show_backup_history(self):
        """Hiển thị lịch sử backup"""
        try:
            with odoo.registry(self.database_name).cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                
                backups = env['th.database.masked.backup'].search([], order='create_date desc', limit=20)
                
                if not backups:
                    print("\nChưa có backup nào được tạo.")
                    return
                
                print(f"\nLỊCH SỬ BACKUP ({len(backups)} bản ghi gần nhất):")
                print("="*80)
                print(f"{'ID':<4} {'Ngày tạo':<20} {'Tên file':<25} {'Trạng thái':<12} {'Kích thước':<12}")
                print("-"*80)
                
                create_date = backup.create_date.strftime('%d/%m/%Y %H:%M') if backup.create_date else 'N/A'
                filename = backup.th_filename[:23] + '...' if len(backup.th_filename or '') > 25 else (backup.th_filename or 'N/A')
                file_size = f"{backup.th_file_size} KB" if backup.th_file_size else 'N/A'
                    
                print(f"{backup.id:<4} {create_date:<20} {filename:<25} {backup.th_status:<10} {file_size:<12}")
                
                print("-"*80)
                
        except Exception as e:
            print(f"Lỗi lấy lịch sử backup: {e}")
    
    def th_show_backup_details(self):
        """Hiển thị chi tiết backup"""
        backup_id = input("\nNhập ID backup để xem chi tiết: ").strip()
        
        try:
            backup_id = int(backup_id)
            
            with odoo.registry(self.database_name).cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                backup = env['th.database.masked.backup'].browse(backup_id)
                
                if not backup.exists():
                    print(f"Không tìm thấy backup với ID {backup_id}")
                    return
                
                print(f"\nCHI TIẾT BACKUP #{backup.id}")
                print("="*60)
                print(f"Tên file: {backup.th_filename}")
                print(f"Định dạng: {backup.th_backup_format}")
                print(f"Trạng thái: {backup.th_status}")
                print(f"Ngày tạo: {backup.create_date}")
                print(f"Kích thước: {backup.th_file_size} KB")
                print(f"Thời gian: {backup.th_duration} phút")
                print(f"Số model đã mask: {backup.th_masked_field_count}")
                
                if backup.th_backup_path:
                    print(f"Đường dẫn: {backup.th_backup_path}")
                
                # Hiển thị masking rules đã áp dụng
                if backup.th_masked_rules_ids:
                    print(f"\nQuy tắc masking đã áp dụng ({len(backup.th_masked_rules_ids)}):")
                    for rule in backup.th_masked_rules_ids:
                        print(f"  - {rule.name} ({rule.model_id.name})")
                
                # Hiển thị log
                if backup.th_log:
                    print(f"\nNhật ký thực thi:")
                    print("-"*40)
                    log_content = backup.th_log.replace('<br/>', '\n').strip()
                    print(log_content)
                
        except ValueError:
            print("ID backup phải là số.")
        except Exception as e:
            print(f"Lỗi lấy chi tiết backup: {e}")
    
    def th_cleanup_old_backups(self):
        """Dọn dẹp backup cũ"""
        try:
            days = input("\nNhập số ngày để xóa backup cũ hơn (mặc định 7): ").strip()
            days = int(days) if days.isdigit() else 7
            
            confirm = input(f"Xác nhận xóa backup cũ hơn {days} ngày? (y/N): ").strip().lower()
            if confirm != 'y':
                print("Hủy dọn dẹp.")
                return
            
            with odoo.registry(self.database_name).cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                
                # Gọi method cleanup
                result = env['th.database.masked.backup'].cleanup_old_backups(days)
                
                print(f"Đã dọn dẹp backup cũ hơn {days} ngày.")
                
        except ValueError:
            print("Số ngày phải là số.")
        except Exception as e:
            print(f"Lỗi dọn dẹp backup: {e}")


def main():
    """Hàm main"""
    parser = argparse.ArgumentParser(description='Odoo Data Masking Backup CLI')
    parser.add_argument('-d', '--database', required=True, help='Tên database Odoo')
    parser.add_argument('-c', '--config', help='Đường dẫn file config Odoo')  # Thay đổi từ --config thành -c, --config
    
    args = parser.parse_args()
    
    # Khởi tạo CLI với config file
    cli = MaskedBackupCLI(args.database, args.config)
    
    # Kết nối database
    if not cli.th_connect_database():
        sys.exit(1)
    
    try:
        # Hiển thị menu chính
        cli.th_show_main_menu()
    except KeyboardInterrupt:
        print("\n\nTạm biệt!")
    except Exception as e:
        print(f"\nLỗi không mong muốn: {e}")


if __name__ == '__main__':
    main()
