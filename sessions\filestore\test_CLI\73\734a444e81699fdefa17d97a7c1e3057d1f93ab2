
/* /web_enterprise/static/src/main.js */
odoo.define('@web_enterprise/main',async function(require){'use strict';let __exports={};const{startWebClient}=require("@web/start");const{WebClientEnterprise}=require("@web_enterprise/webclient/webclient");startWebClient(WebClientEnterprise);return __exports;});;

/* /web/static/src/start.js */
odoo.define('@web/start',async function(require){'use strict';let __exports={};const{makeEnv,startServices}=require("@web/env");const{legacySetupProm}=require("@web/legacy/legacy_setup");const{mapLegacyEnvToWowlEnv}=require("@web/legacy/utils");const{localization}=require("@web/core/l10n/localization");const{session}=require("@web/session");const{renderToString}=require("@web/core/utils/render");const{setLoadXmlDefaultApp,templates}=require("@web/core/assets");const{hasTouch}=require("@web/core/browser/feature_detection");const{App,whenReady}=require("@odoo/owl");__exports.startWebClient=startWebClient;async function startWebClient(Webclient){odoo.info={db:session.db,server_version:session.server_version,server_version_info:session.server_version_info,isEnterprise:session.server_version_info.slice(-1)[0]==="e",};odoo.isReady=false;const env=makeEnv();await startServices(env);await whenReady();const legacyEnv=await legacySetupProm;mapLegacyEnvToWowlEnv(legacyEnv,env);const app=new App(Webclient,{env,templates,dev:env.debug,translatableAttributes:["data-tooltip"],translateFn:env._t,});renderToString.app=app;setLoadXmlDefaultApp(app);const root=await app.mount(document.body);const classList=document.body.classList;if(localization.direction==="rtl"){classList.add("o_rtl");}
if(env.services.user.userId===1){classList.add("o_is_superuser");}
if(env.debug){classList.add("o_debug");}
if(hasTouch()){classList.add("o_touch_device");}
odoo.__WOWL_DEBUG__={root};odoo.isReady=true;const favicon=`/web/image/res.company/${env.services.company.currentCompany.id}/favicon`;const icons=document.querySelectorAll("link[rel*='icon']");const msIcon=document.querySelector("meta[name='msapplication-TileImage']");for(const icon of icons){icon.href=favicon;}
if(msIcon){msIcon.content=favicon;}}
return __exports;});;

/* /web/static/src/legacy/legacy_setup.js */
odoo.define('@web/legacy/legacy_setup',async function(require){'use strict';let __exports={};const{registry}=require("@web/core/registry");const{makeLegacyNotificationService,makeLegacyRpcService,makeLegacySessionService,makeLegacyDialogMappingService,makeLegacyCrashManagerService,makeLegacyCommandService,makeLegacyDropdownService,}=require("@web/legacy/utils");const{makeLegacyActionManagerService}=require("@web/legacy/backend_utils");const AbstractService=require("web.AbstractService");const legacyEnv=require("web.env");const session=require("web.session");const makeLegacyWebClientService=require("web.pseudo_web_client");const{templates}=require("@web/core/assets");const{Component,whenReady}=require("@odoo/owl");let legacySetupResolver;const legacySetupProm=__exports.legacySetupProm=new Promise((resolve)=>{legacySetupResolver=resolve;});(async()=>{AbstractService.prototype.deployServices(legacyEnv);Component.env=legacyEnv;const legacyActionManagerService=makeLegacyActionManagerService(legacyEnv);const serviceRegistry=registry.category("services");serviceRegistry.add("legacy_action_manager",legacyActionManagerService);const legacyRpcService=makeLegacyRpcService(legacyEnv);serviceRegistry.add("legacy_rpc",legacyRpcService);const legacySessionService=makeLegacySessionService(legacyEnv,session);serviceRegistry.add("legacy_session",legacySessionService);const legacyWebClientService=makeLegacyWebClientService(legacyEnv);serviceRegistry.add("legacy_web_client",legacyWebClientService);serviceRegistry.add("legacy_notification",makeLegacyNotificationService(legacyEnv));serviceRegistry.add("legacy_crash_manager",makeLegacyCrashManagerService(legacyEnv));const legacyDialogMappingService=makeLegacyDialogMappingService(legacyEnv);serviceRegistry.add("legacy_dialog_mapping",legacyDialogMappingService);const legacyCommandService=makeLegacyCommandService(legacyEnv);serviceRegistry.add("legacy_command",legacyCommandService);serviceRegistry.add("legacy_dropdown",makeLegacyDropdownService(legacyEnv));const wowlToLegacyServiceMappers=registry.category("wowlToLegacyServiceMappers").getEntries();for(const[legacyServiceName,wowlToLegacyServiceMapper]of wowlToLegacyServiceMappers){serviceRegistry.add(legacyServiceName,wowlToLegacyServiceMapper(legacyEnv));}
await Promise.all([whenReady(),session.is_bound]);legacyEnv.templates=templates;legacySetupResolver(legacyEnv);})();return __exports;});odoo.define(`web.legacySetup`,async function(require){return require('@web/legacy/legacy_setup')[Symbol.for("default")];});